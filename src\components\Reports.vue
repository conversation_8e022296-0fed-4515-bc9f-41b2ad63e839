<template>
  <div class="reports">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><DataAnalysis /></el-icon>
        统计报表
      </h2>
      <p class="page-subtitle">查看收支趋势和财务分析</p>
    </div>

    <!-- 时间筛选 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <el-form inline class="filter-form">
          <el-form-item label="时间范围">
            <el-select v-model="timeRange" @change="updateCharts" style="width: 150px">
              <el-option label="本周" value="week" />
              <el-option label="本月" value="month" />
              <el-option label="本季度" value="quarter" />
              <el-option label="本年" value="year" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="成员">
            <el-select v-model="selectedMember" @change="updateCharts" style="width: 150px">
              <el-option label="全部成员" value="all" />
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="张小明" value="张小明" />
              <el-option label="张奶奶" value="张奶奶" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="exportReport" icon="Download">导出报表</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 统计概览 -->
    <div class="overview-section">
      <div class="overview-grid">
        <div class="overview-card income">
          <div class="card-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="card-content">
            <h3>总收入</h3>
            <p class="amount">¥{{ totalIncome.toLocaleString() }}</p>
            <span class="change" :class="incomeChange >= 0 ? 'positive' : 'negative'">
              {{ incomeChange >= 0 ? '+' : '' }}{{ incomeChange }}%
            </span>
          </div>
        </div>

        <div class="overview-card expense">
          <div class="card-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="card-content">
            <h3>总支出</h3>
            <p class="amount">¥{{ totalExpense.toLocaleString() }}</p>
            <span class="change" :class="expenseChange >= 0 ? 'negative' : 'positive'">
              {{ expenseChange >= 0 ? '+' : '' }}{{ expenseChange }}%
            </span>
          </div>
        </div>

        <div class="overview-card balance">
          <div class="card-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="card-content">
            <h3>净收入</h3>
            <p class="amount" :class="netIncome >= 0 ? 'positive' : 'negative'">
              ¥{{ netIncome.toLocaleString() }}
            </p>
            <span class="change">{{ savingsRate }}% 储蓄率</span>
          </div>
        </div>

        <div class="overview-card transactions">
          <div class="card-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="card-content">
            <h3>交易笔数</h3>
            <p class="amount">{{ totalTransactions }}笔</p>
            <span class="change">平均 ¥{{ avgTransaction.toLocaleString() }}/笔</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- 收支趋势图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>收支趋势</h3>
            <el-button type="text" size="small" @click="refreshChart">刷新</el-button>
          </div>
          <div class="chart-container">
            <div class="chart-placeholder">
              <el-icon class="chart-icon"><TrendCharts /></el-icon>
              <p>收支趋势图</p>
              <p class="chart-desc">显示{{ getTimeRangeLabel() }}的收入和支出变化趋势</p>
            </div>
          </div>
        </div>

        <!-- 支出分类饼图 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>支出分类</h3>
            <el-button type="text" size="small" @click="viewCategoryDetails">详情</el-button>
          </div>
          <div class="chart-container">
            <div class="category-chart">
              <div
                v-for="category in expenseCategories"
                :key="category.name"
                class="category-item"
              >
                <div class="category-info">
                  <span class="category-name">{{ category.name }}</span>
                  <span class="category-amount">¥{{ category.amount.toLocaleString() }}</span>
                </div>
                <div class="category-progress">
                  <el-progress
                    :percentage="category.percentage"
                    :color="category.color"
                    :show-text="false"
                    :stroke-width="8"
                  />
                </div>
                <div class="category-percent">{{ category.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 成员对比 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>成员对比</h3>
            <el-button type="text" size="small" @click="viewMemberDetails">详情</el-button>
          </div>
          <div class="chart-container">
            <div class="member-comparison">
              <div
                v-for="member in memberStats"
                :key="member.name"
                class="member-item"
              >
                <div class="member-info">
                  <el-avatar :src="member.avatar" :size="40">{{ member.name[0] }}</el-avatar>
                  <div class="member-details">
                    <p class="member-name">{{ member.name }}</p>
                    <p class="member-role">{{ member.role }}</p>
                  </div>
                </div>
                <div class="member-stats">
                  <div class="stat-row">
                    <span class="stat-label">收入</span>
                    <span class="stat-value income">¥{{ member.income.toLocaleString() }}</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">支出</span>
                    <span class="stat-value expense">¥{{ member.expense.toLocaleString() }}</span>
                  </div>
                  <div class="stat-row">
                    <span class="stat-label">净额</span>
                    <span class="stat-value" :class="member.net >= 0 ? 'income' : 'expense'">
                      ¥{{ member.net.toLocaleString() }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 账户分布 -->
        <div class="chart-card">
          <div class="chart-header">
            <h3>账户分布</h3>
            <el-button type="text" size="small" @click="viewAccountDetails">详情</el-button>
          </div>
          <div class="chart-container">
            <div class="account-distribution">
              <div
                v-for="account in accountStats"
                :key="account.name"
                class="account-item"
              >
                <div class="account-icon" :class="account.type">
                  <el-icon v-if="account.type === 'bank'"><CreditCard /></el-icon>
                  <el-icon v-else-if="account.type === 'alipay'"><Money /></el-icon>
                  <el-icon v-else-if="account.type === 'wechat'"><ChatDotRound /></el-icon>
                  <el-icon v-else><Wallet /></el-icon>
                </div>
                <div class="account-info">
                  <p class="account-name">{{ account.name }}</p>
                  <p class="account-balance">¥{{ account.balance.toLocaleString() }}</p>
                  <div class="account-bar">
                    <div
                      class="account-fill"
                      :style="{ width: account.percentage + '%', backgroundColor: account.color }"
                    ></div>
                  </div>
                </div>
                <div class="account-percent">{{ account.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <el-card class="table-card">
        <template #header>
          <div class="card-header">
            <span>详细数据</span>
            <div class="header-actions">
              <el-button type="text" @click="exportData" icon="Download">导出</el-button>
            </div>
          </div>
        </template>

        <el-tabs v-model="activeTab" class="data-tabs">
          <el-tab-pane label="分类统计" name="category">
            <el-table :data="categoryTableData" style="width: 100%">
              <el-table-column prop="category" label="分类" width="120" />
              <el-table-column prop="income" label="收入" width="120">
                <template #default="scope">
                  <span class="amount-income">¥{{ scope.row.income.toLocaleString() }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="expense" label="支出" width="120">
                <template #default="scope">
                  <span class="amount-expense">¥{{ scope.row.expense.toLocaleString() }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="net" label="净额" width="120">
                <template #default="scope">
                  <span :class="scope.row.net >= 0 ? 'amount-income' : 'amount-expense'">
                    ¥{{ scope.row.net.toLocaleString() }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="transactions" label="交易笔数" width="100" />
              <el-table-column prop="percentage" label="占比" width="80">
                <template #default="scope">
                  {{ scope.row.percentage }}%
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="成员统计" name="member">
            <el-table :data="memberTableData" style="width: 100%">
              <el-table-column prop="name" label="成员" width="100" />
              <el-table-column prop="role" label="角色" width="100" />
              <el-table-column prop="income" label="收入" width="120">
                <template #default="scope">
                  <span class="amount-income">¥{{ scope.row.income.toLocaleString() }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="expense" label="支出" width="120">
                <template #default="scope">
                  <span class="amount-expense">¥{{ scope.row.expense.toLocaleString() }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="net" label="净收入" width="120">
                <template #default="scope">
                  <span :class="scope.row.net >= 0 ? 'amount-income' : 'amount-expense'">
                    ¥{{ scope.row.net.toLocaleString() }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="transactions" label="交易笔数" width="100" />
              <el-table-column prop="avgTransaction" label="平均金额" width="120">
                <template #default="scope">
                  ¥{{ scope.row.avgTransaction.toLocaleString() }}
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'

export default {
  name: 'Reports',
  setup() {
    const timeRange = ref('month')
    const selectedMember = ref('all')
    const activeTab = ref('category')

    // 模拟统计数据
    const totalIncome = ref(25800)
    const totalExpense = ref(18600)
    const totalTransactions = ref(156)
    const incomeChange = ref(12.5)
    const expenseChange = ref(8.3)

    const netIncome = computed(() => totalIncome.value - totalExpense.value)
    const savingsRate = computed(() => Math.round((netIncome.value / totalIncome.value) * 100))
    const avgTransaction = computed(() => Math.round((totalIncome.value + totalExpense.value) / totalTransactions.value))

    // 支出分类数据
    const expenseCategories = reactive([
      { name: '餐饮', amount: 4200, percentage: 85, color: '#ff6b6b' },
      { name: '交通', amount: 1800, percentage: 60, color: '#4ecdc4' },
      { name: '购物', amount: 3200, percentage: 75, color: '#45b7d1' },
      { name: '娱乐', amount: 1200, percentage: 40, color: '#96ceb4' },
      { name: '医疗', amount: 800, percentage: 25, color: '#feca57' },
      { name: '教育', amount: 1500, percentage: 50, color: '#ff9ff3' }
    ])

    // 成员统计数据
    const memberStats = reactive([
      {
        name: '张三',
        role: '户主',
        avatar: '/src/assets/preview.jpg',
        income: 12000,
        expense: 8600,
        net: 3400
      },
      {
        name: '李四',
        role: '配偶',
        avatar: '/src/assets/preview.jpg',
        income: 9500,
        expense: 6200,
        net: 3300
      },
      {
        name: '张小明',
        role: '子女',
        avatar: '/src/assets/preview.jpg',
        income: 800,
        expense: 2800,
        net: -2000
      },
      {
        name: '张奶奶',
        role: '长辈',
        avatar: '/src/assets/preview.jpg',
        income: 3000,
        expense: 1000,
        net: 2000
      }
    ])

    // 账户统计数据
    const accountStats = reactive([
      { name: '工商银行', type: 'bank', balance: 25600, percentage: 51, color: '#1890ff' },
      { name: '建设银行', type: 'bank', balance: 18900, percentage: 38, color: '#722ed1' },
      { name: '支付宝', type: 'alipay', balance: 3200, percentage: 6, color: '#1677ff' },
      { name: '微信', type: 'wechat', balance: 1800, percentage: 4, color: '#52c41a' },
      { name: '现金', type: 'cash', balance: 500, percentage: 1, color: '#fa8c16' }
    ])

    // 分类表格数据
    const categoryTableData = reactive([
      { category: '餐饮', income: 0, expense: 4200, net: -4200, transactions: 45, percentage: 23 },
      { category: '交通', income: 0, expense: 1800, net: -1800, transactions: 28, percentage: 10 },
      { category: '购物', income: 0, expense: 3200, net: -3200, transactions: 22, percentage: 17 },
      { category: '工资', income: 21500, expense: 0, net: 21500, transactions: 2, percentage: 83 },
      { category: '兼职', income: 2800, expense: 0, net: 2800, transactions: 8, percentage: 11 }
    ])

    // 成员表格数据
    const memberTableData = reactive([
      { name: '张三', role: '户主', income: 12000, expense: 8600, net: 3400, transactions: 45, avgTransaction: 458 },
      { name: '李四', role: '配偶', income: 9500, expense: 6200, net: 3300, transactions: 38, avgTransaction: 413 },
      { name: '张小明', role: '子女', income: 800, expense: 2800, net: -2000, transactions: 25, avgTransaction: 144 },
      { name: '张奶奶', role: '长辈', income: 3000, expense: 1000, net: 2000, transactions: 12, avgTransaction: 333 }
    ])

    return {
      timeRange,
      selectedMember,
      activeTab,
      totalIncome,
      totalExpense,
      totalTransactions,
      incomeChange,
      expenseChange,
      netIncome,
      savingsRate,
      avgTransaction,
      expenseCategories,
      memberStats,
      accountStats,
      categoryTableData,
      memberTableData
    }
  },
  methods: {
    // 获取时间范围标签
    getTimeRangeLabel() {
      const labels = {
        'week': '本周',
        'month': '本月',
        'quarter': '本季度',
        'year': '本年'
      }
      return labels[this.timeRange] || '本月'
    },

    // 更新图表
    updateCharts() {
      this.$message.info('图表更新中...')
    },

    // 刷新图表
    refreshChart() {
      this.$message.info('图表刷新中...')
    },

    // 查看分类详情
    viewCategoryDetails() {
      this.activeTab = 'category'
      this.$nextTick(() => {
        document.querySelector('.table-section').scrollIntoView({ behavior: 'smooth' })
      })
    },

    // 查看成员详情
    viewMemberDetails() {
      this.activeTab = 'member'
      this.$nextTick(() => {
        document.querySelector('.table-section').scrollIntoView({ behavior: 'smooth' })
      })
    },

    // 查看账户详情
    viewAccountDetails() {
      this.$router.push('/admin/Accounts')
    },

    // 导出报表
    exportReport() {
      this.$message.info('导出功能开发中...')
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...')
    }
  }
}
</script>

<style scoped>
.reports {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 25px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 25px;
}

.filter-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.filter-form {
  margin: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.filter-form .el-form-item:last-child {
  margin-right: 0;
}

/* 概览区域 */
.overview-section {
  margin-bottom: 25px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.overview-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.overview-card.income .card-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.overview-card.expense .card-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.overview-card.balance .card-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.overview-card.transactions .card-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.card-content h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

.card-content .amount {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.card-content .amount.positive {
  color: #52c41a;
}

.card-content .amount.negative {
  color: #ff4d4f;
}

.card-content .change {
  font-size: 12px;
  font-weight: 500;
}

.change.positive {
  color: #52c41a;
}

.change.negative {
  color: #ff4d4f;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 25px;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.chart-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图表占位符 */
.chart-placeholder {
  text-align: center;
  color: #7f8c8d;
}

.chart-icon {
  font-size: 48px;
  margin-bottom: 10px;
  color: #d9d9d9;
}

.chart-placeholder p {
  margin: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

.chart-desc {
  font-size: 12px !important;
  color: #bdc3c7 !important;
}

/* 分类图表 */
.category-chart {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-info {
  min-width: 100px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.category-amount {
  font-size: 12px;
  color: #7f8c8d;
}

.category-progress {
  flex: 1;
}

.category-percent {
  min-width: 40px;
  text-align: right;
  font-size: 12px;
  color: #7f8c8d;
}

/* 成员对比 */
.member-comparison {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 120px;
}

.member-details {
  flex: 1;
}

.member-name {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.member-role {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
}

.member-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.stat-value {
  font-size: 12px;
  font-weight: 600;
}

.stat-value.income {
  color: #52c41a;
}

.stat-value.expense {
  color: #ff4d4f;
}

/* 账户分布 */
.account-distribution {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.account-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.account-icon.bank {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.account-icon.alipay {
  background: linear-gradient(135deg, #1677ff, #69b1ff);
}

.account-icon.wechat {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.account-icon.cash {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.account-info {
  flex: 1;
}

.account-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.account-balance {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: #7f8c8d;
}

.account-bar {
  width: 100%;
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.account-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.account-percent {
  min-width: 40px;
  text-align: right;
  font-size: 12px;
  color: #7f8c8d;
}

/* 表格区域 */
.table-section {
  margin-bottom: 20px;
}

.table-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 表格样式 */
.amount-income {
  color: #52c41a;
  font-weight: 600;
}

.amount-expense {
  color: #ff4d4f;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .reports {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .charts-grid {
    gap: 15px;
  }

  .chart-card {
    padding: 15px;
    min-height: 250px;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .member-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .member-info {
    min-width: auto;
  }

  .member-stats {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .reports {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .overview-card {
    padding: 15px;
    gap: 10px;
  }

  .card-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .card-content .amount {
    font-size: 20px;
  }

  .chart-card {
    padding: 12px;
    min-height: 200px;
  }

  .category-item,
  .account-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .category-progress,
  .account-info {
    width: 100%;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table th) {
  background: rgba(255, 255, 255, 0.6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-table tr:hover > td) {
  background: rgba(255, 255, 255, 0.8);
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(0, 0, 0, 0.1);
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

:deep(.el-avatar) {
  border: 2px solid rgba(255, 255, 255, 0.8);
}
</style>
