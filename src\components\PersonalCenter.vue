<template>
  <div class="personal-center">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><User /></el-icon>
        个人中心
      </h2>
      <p class="page-subtitle">管理您的个人信息和账户设置</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <!-- 左侧个人信息卡片 -->
      <div class="left-panel">
        <div class="profile-card">
          <div class="profile-header">
            <div class="avatar-section">
              <AvatarUpload
                :avatar-url="userInfo.avatar"
                :alt="userInfo.nickname"
                :size="90"
                @avatar-change="handleAvatarChange"
              />
            </div>

            <div class="profile-info">
              <h3 class="profile-name">{{ userInfo.nickname || '未设置昵称' }}</h3>
              <p class="profile-email">{{ userInfo.email }}</p>
              <div class="profile-stats">
                <div class="stat-item">
                  <span class="stat-label">注册时间</span>
                  <span class="stat-value">{{ formatDate(userInfo.registerTime) }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">最后登录</span>
                  <span class="stat-value">{{ formatDate(userInfo.lastLogin) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户统计卡片 -->
        <UserStatsCard />

        <!-- 快速设置 -->
        <QuickSettings />
      </div>

      <!-- 右侧设置面板 -->
      <div class="settings-panel">
        <el-tabs v-model="activeTab" class="settings-tabs">
          <!-- 基本信息标签页 -->
          <el-tab-pane label="基本信息" name="basic">
            <div class="tab-content">
              <el-form
                :model="editForm"
                :rules="formRules"
                ref="editFormRef"
                label-width="100px"
                class="edit-form"
              >
                <el-form-item label="昵称" prop="nickname">
                  <el-input
                    v-model="editForm.nickname"
                    placeholder="请输入昵称"
                    clearable
                    maxlength="20"
                    show-word-limit
                  >
                    <template #prefix>
                      <el-icon><User /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="邮箱" prop="email">
                  <el-input
                    v-model="editForm.email"
                    placeholder="请输入邮箱"
                    disabled
                  >
                    <template #prefix>
                      <el-icon><Message /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="手机号" prop="phone">
                  <el-input
                    v-model="editForm.phone"
                    placeholder="请输入手机号"
                    clearable
                    maxlength="11"
                  >
                    <template #prefix>
                      <el-icon><Phone /></el-icon>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="个人简介" prop="bio">
                  <el-input
                    v-model="editForm.bio"
                    type="textarea"
                    placeholder="介绍一下自己吧..."
                    :rows="4"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>

                <el-form-item label="生日" prop="birthday">
                  <el-date-picker
                    v-model="editForm.birthday"
                    type="date"
                    placeholder="选择生日"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                </el-form-item>

                <el-form-item label="性别" prop="gender">
                  <el-radio-group v-model="editForm.gender">
                    <el-radio value="male">男</el-radio>
                    <el-radio value="female">女</el-radio>
                    <el-radio value="other">其他</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    @click="updateBasicInfo"
                    :loading="updating"
                    icon="Check"
                  >
                    保存修改
                  </el-button>
                  <el-button @click="resetForm" icon="Refresh">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 安全设置标签页 -->
          <el-tab-pane label="安全设置" name="security">
            <div class="tab-content">
              <div class="security-section">
                <div class="security-item">
                  <div class="security-info">
                    <h4>修改密码</h4>
                    <p>定期更换密码，保护账户安全</p>
                  </div>
                  <el-button type="primary" plain @click="showPasswordDialog = true">
                    修改密码
                  </el-button>
                </div>

                <div class="security-item">
                  <div class="security-info">
                    <h4>登录设备</h4>
                    <p>管理您的登录设备和会话</p>
                  </div>
                  <el-button type="info" plain @click="viewLoginDevices">
                    查看设备
                  </el-button>
                </div>

                <div class="security-item">
                  <div class="security-info">
                    <h4>账户注销</h4>
                    <p>永久删除您的账户和所有数据</p>
                  </div>
                  <el-button type="danger" plain @click="showDeleteDialog = true">
                    注销账户
                  </el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 偏好设置标签页 -->
          <el-tab-pane label="偏好设置" name="preferences">
            <div class="tab-content">
              <el-form label-width="120px" class="preferences-form">
                <el-form-item label="主题模式">
                  <el-radio-group v-model="preferences.theme">
                    <el-radio value="light">浅色模式</el-radio>
                    <el-radio value="dark">深色模式</el-radio>
                    <el-radio value="auto">跟随系统</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="语言设置">
                  <el-select v-model="preferences.language" placeholder="选择语言">
                    <el-option label="简体中文" value="zh-CN" />
                    <el-option label="English" value="en-US" />
                    <el-option label="日本語" value="ja-JP" />
                  </el-select>
                </el-form-item>

                <el-form-item label="邮件通知">
                  <el-switch
                    v-model="preferences.emailNotification"
                    active-text="开启"
                    inactive-text="关闭"
                  />
                </el-form-item>

                <el-form-item label="桌面通知">
                  <el-switch
                    v-model="preferences.desktopNotification"
                    active-text="开启"
                    inactive-text="关闭"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="savePreferences">
                    保存设置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="400px"
      :before-close="handlePasswordDialogClose"
    >
      <el-form
        :model="passwordForm"
        :rules="passwordRules"
        ref="passwordFormRef"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" @click="updatePassword" :loading="passwordUpdating">
          确认修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 账户注销确认对话框 -->
    <el-dialog
      v-model="showDeleteDialog"
      title="账户注销确认"
      width="400px"
    >
      <div class="delete-warning">
        <el-icon class="warning-icon"><WarningFilled /></el-icon>
        <p>此操作将永久删除您的账户和所有相关数据，且无法恢复。</p>
        <p>请输入您的密码以确认此操作：</p>
        <el-input
          v-model="deleteConfirmPassword"
          type="password"
          placeholder="请输入密码确认"
          show-password
          style="margin-top: 15px;"
        />
      </div>
      <template #footer>
        <el-button @click="showDeleteDialog = false">取消</el-button>
        <el-button type="danger" @click="deleteAccount" :loading="deleting">
          确认注销
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { ref, reactive } from 'vue'
import dayjs from 'dayjs'
import AvatarUpload from './AvatarUpload.vue'
import UserStatsCard from './UserStatsCard.vue'
import QuickSettings from './QuickSettings.vue'

export default {
  name: 'PersonalCenter',
  components: {
    AvatarUpload,
    UserStatsCard,
    QuickSettings
  },
  setup() {
    // 响应式数据
    const activeTab = ref('basic')
    const updating = ref(false)
    const passwordUpdating = ref(false)
    const deleting = ref(false)
    const showPasswordDialog = ref(false)
    const showDeleteDialog = ref(false)
    const deleteConfirmPassword = ref('')
    const avatarInput = ref(null)
    const editFormRef = ref(null)
    const passwordFormRef = ref(null)

    // 用户信息
    const userInfo = reactive({
      nickname: localStorage.getItem('nickname') || '',
      email: localStorage.getItem('nickname') || '<EMAIL>',
      avatar: localStorage.getItem('userAvatar') || '/src/assets/preview.jpg',
      phone: localStorage.getItem('userPhone') || '',
      bio: localStorage.getItem('userBio') || '',
      birthday: localStorage.getItem('userBirthday') || '',
      gender: localStorage.getItem('userGender') || '',
      registerTime: localStorage.getItem('registerTime') || new Date().toISOString(),
      lastLogin: new Date().toISOString()
    })

    // 编辑表单
    const editForm = reactive({
      nickname: userInfo.nickname,
      email: userInfo.email,
      phone: userInfo.phone,
      bio: userInfo.bio,
      birthday: userInfo.birthday,
      gender: userInfo.gender
    })

    // 密码表单
    const passwordForm = reactive({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })

    // 偏好设置
    const preferences = reactive({
      theme: localStorage.getItem('theme') || 'light',
      language: localStorage.getItem('language') || 'zh-CN',
      emailNotification: localStorage.getItem('emailNotification') === 'true',
      desktopNotification: localStorage.getItem('desktopNotification') === 'true'
    })

    // 表单验证规则
    const formRules = {
      nickname: [
        { required: true, message: '请输入昵称', trigger: 'blur' },
        { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ]
    }

    const passwordRules = {
      currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' }
      ],
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== passwordForm.newPassword) {
              callback(new Error('两次输入密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }

    return {
      activeTab,
      updating,
      passwordUpdating,
      deleting,
      showPasswordDialog,
      showDeleteDialog,
      deleteConfirmPassword,
      avatarInput,
      editFormRef,
      passwordFormRef,
      userInfo,
      editForm,
      passwordForm,
      preferences,
      formRules,
      passwordRules
    }
  },
  methods: {
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未知'
      return dayjs(dateString).format('YYYY-MM-DD HH:mm')
    },

    // 处理头像变更
    handleAvatarChange(avatarDataUrl) {
      this.userInfo.avatar = avatarDataUrl
      localStorage.setItem('userAvatar', avatarDataUrl)
    },

    // 更新基本信息
    async updateBasicInfo() {
      try {
        const valid = await this.$refs.editFormRef.validate()
        if (!valid) return

        this.updating = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 更新用户信息
        Object.assign(this.userInfo, this.editForm)

        // 保存到本地存储
        localStorage.setItem('nickname', this.editForm.nickname)
        localStorage.setItem('userPhone', this.editForm.phone)
        localStorage.setItem('userBio', this.editForm.bio)
        localStorage.setItem('userBirthday', this.editForm.birthday)
        localStorage.setItem('userGender', this.editForm.gender)

        this.$message.success('个人信息更新成功')
      } catch (error) {
        this.$message.error('更新失败，请重试')
      } finally {
        this.updating = false
      }
    },

    // 重置表单
    resetForm() {
      Object.assign(this.editForm, {
        nickname: this.userInfo.nickname,
        email: this.userInfo.email,
        phone: this.userInfo.phone,
        bio: this.userInfo.bio,
        birthday: this.userInfo.birthday,
        gender: this.userInfo.gender
      })
      this.$refs.editFormRef?.clearValidate()
    },

    // 更新密码
    async updatePassword() {
      try {
        const valid = await this.$refs.passwordFormRef.validate()
        if (!valid) return

        this.passwordUpdating = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        this.$message.success('密码修改成功')
        this.showPasswordDialog = false
        this.resetPasswordForm()
      } catch (error) {
        this.$message.error('密码修改失败，请重试')
      } finally {
        this.passwordUpdating = false
      }
    },

    // 重置密码表单
    resetPasswordForm() {
      Object.assign(this.passwordForm, {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      this.$refs.passwordFormRef?.clearValidate()
    },

    // 处理密码对话框关闭
    handlePasswordDialogClose() {
      this.resetPasswordForm()
      this.showPasswordDialog = false
    },

    // 查看登录设备
    viewLoginDevices() {
      this.$message.info('登录设备管理功能开发中...')
    },

    // 删除账户
    async deleteAccount() {
      if (!this.deleteConfirmPassword) {
        this.$message.error('请输入密码确认')
        return
      }

      try {
        this.deleting = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 清除本地存储
        localStorage.clear()

        this.$message.success('账户已注销')
        this.$router.push('/')
      } catch (error) {
        this.$message.error('注销失败，请重试')
      } finally {
        this.deleting = false
        this.showDeleteDialog = false
        this.deleteConfirmPassword = ''
      }
    },

    // 保存偏好设置
    savePreferences() {
      localStorage.setItem('theme', this.preferences.theme)
      localStorage.setItem('language', this.preferences.language)
      localStorage.setItem('emailNotification', this.preferences.emailNotification)
      localStorage.setItem('desktopNotification', this.preferences.desktopNotification)

      this.$message.success('偏好设置已保存')
    }
  },

  mounted() {
    // 初始化注册时间
    if (!localStorage.getItem('registerTime')) {
      localStorage.setItem('registerTime', new Date().toISOString())
    }
  }
}
</script>
<style scoped>
.personal-center {
  width: 100%;
  height: 100%;
  padding: 15px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 28px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 13px;
  margin: 0;
}

/* 内容包装器 */
.content-wrapper {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  height: calc(100% - 80px);
}

/* 左侧面板 */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  max-height: 100%;
  padding-right: 5px;
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar {
  width: 6px;
}

.left-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.left-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 个人信息卡片 */
.profile-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.profile-header {
  text-align: center;
}

/* 头像部分 */
.avatar-section {
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
}

/* 个人信息 */
.profile-info {
  text-align: center;
}

.profile-name {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 6px 0;
}

.profile-email {
  color: #7f8c8d;
  font-size: 13px;
  margin: 0 0 15px 0;
}

.profile-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 13px;
  color: #7f8c8d;
}

.stat-value {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 500;
}

/* 设置面板 */
.settings-panel {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.settings-panel::-webkit-scrollbar {
  width: 6px;
}

.settings-panel::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.settings-panel::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.settings-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.settings-tabs {
  height: 100%;
}

.tab-content {
  padding: 15px 0;
}

/* 表单样式 */
.edit-form {
  max-width: 500px;
}

.edit-form .el-form-item {
  margin-bottom: 16px;
}

/* 安全设置 */
.security-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.security-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 16px;
}

.security-info p {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

/* 偏好设置 */
.preferences-form {
  max-width: 400px;
}

.preferences-form .el-form-item {
  margin-bottom: 20px;
}

/* 对话框样式 */
.delete-warning {
  text-align: center;
  padding: 20px 0;
}

.warning-icon {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 15px;
}

.delete-warning p {
  margin: 10px 0;
  color: #606266;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-wrapper {
    grid-template-columns: 320px 1fr;
    gap: 15px;
  }
}

@media (max-width: 1024px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 15px;
    height: auto;
  }

  .left-panel {
    order: 2;
  }

  .settings-panel {
    order: 1;
  }
}

@media (max-width: 768px) {
  .personal-center {
    padding: 10px;
  }

  .page-header {
    margin-bottom: 15px;
  }

  .page-title {
    font-size: 20px;
  }

  .profile-card,
  .settings-panel {
    padding: 15px;
  }

  .avatar-preview {
    width: 70px !important;
    height: 70px !important;
  }

  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 12px;
  }

  .content-wrapper {
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .personal-center {
    padding: 8px;
  }

  .page-title {
    font-size: 18px;
  }

  .profile-card,
  .settings-panel {
    padding: 12px;
  }

  .avatar-preview {
    width: 60px !important;
    height: 60px !important;
  }

  .profile-name {
    font-size: 18px;
  }

  .stat-item {
    padding: 4px 0;
  }

  .security-item {
    padding: 10px;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(0, 0, 0, 0.1);
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-dialog) {
  border-radius: 16px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}
</style>