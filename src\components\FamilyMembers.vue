<template>
  <div class="family-members">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><User /></el-icon>
        家庭成员管理
      </h2>
      <p class="page-subtitle">管理家庭成员信息和权限设置</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showAddDialog = true" icon="Plus">添加成员</el-button>
      <el-button type="info" @click="exportMembers" icon="Download">导出数据</el-button>
    </div>

    <!-- 成员列表 -->
    <div class="members-grid">
      <div
        v-for="member in familyMembers"
        :key="member.id"
        class="member-card"
      >
        <div class="member-header">
          <el-avatar :src="member.avatar" :size="80">{{ member.name[0] }}</el-avatar>
          <div class="member-status" :class="member.status">
            {{ member.status === 'active' ? '活跃' : '非活跃' }}
          </div>
        </div>

        <div class="member-info">
          <h3 class="member-name">{{ member.name }}</h3>
          <p class="member-role">{{ member.role }}</p>
          <p class="member-relation">{{ member.relation }}</p>
        </div>

        <div class="member-stats">
          <div class="stat-row">
            <span class="stat-label">本月收入:</span>
            <span class="stat-value income">¥{{ member.monthlyIncome.toLocaleString() }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">本月支出:</span>
            <span class="stat-value expense">¥{{ member.monthlyExpense.toLocaleString() }}</span>
          </div>
          <div class="stat-row">
            <span class="stat-label">净收入:</span>
            <span class="stat-value" :class="member.netIncome >= 0 ? 'income' : 'expense'">
              ¥{{ member.netIncome.toLocaleString() }}
            </span>
          </div>
        </div>

        <div class="member-permissions">
          <h4>权限设置</h4>
          <div class="permission-list">
            <el-tag
              v-for="permission in member.permissions"
              :key="permission"
              size="small"
              :type="getPermissionType(permission)"
            >
              {{ getPermissionLabel(permission) }}
            </el-tag>
          </div>
        </div>

        <div class="member-actions">
          <el-button type="primary" size="small" @click="editMember(member)" icon="Edit">编辑</el-button>
          <el-button type="info" size="small" @click="viewMemberDetails(member)" icon="View">详情</el-button>
          <el-button
            type="danger"
            size="small"
            @click="deleteMember(member)"
            icon="Delete"
            :disabled="member.role === '户主'"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加/编辑成员对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingMember ? '编辑成员' : '添加成员'"
      width="500px"
      @close="resetMemberForm"
    >
      <el-form
        :model="memberForm"
        :rules="memberRules"
        ref="memberFormRef"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="memberForm.name" placeholder="请输入姓名" />
        </el-form-item>

        <el-form-item label="关系" prop="relation">
          <el-select v-model="memberForm.relation" placeholder="请选择关系" style="width: 100%">
            <el-option label="配偶" value="配偶" />
            <el-option label="子女" value="子女" />
            <el-option label="父母" value="父母" />
            <el-option label="兄弟姐妹" value="兄弟姐妹" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="memberForm.role" placeholder="请选择角色" style="width: 100%">
            <el-option label="管理员" value="管理员" />
            <el-option label="成员" value="成员" />
            <el-option label="只读" value="只读" />
          </el-select>
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="memberForm.phone" placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="memberForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="生日" prop="birthday">
          <el-date-picker
            v-model="memberForm.birthday"
            type="date"
            placeholder="选择生日"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="权限设置" prop="permissions">
          <el-checkbox-group v-model="memberForm.permissions">
            <el-checkbox value="view_records">查看记录</el-checkbox>
            <el-checkbox value="add_records">添加记录</el-checkbox>
            <el-checkbox value="edit_records">编辑记录</el-checkbox>
            <el-checkbox value="delete_records">删除记录</el-checkbox>
            <el-checkbox value="manage_budget">预算管理</el-checkbox>
            <el-checkbox value="view_reports">查看报表</el-checkbox>
            <el-checkbox value="manage_accounts">账户管理</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="memberForm.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveMember" :loading="saving">
          {{ editingMember ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 成员详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="成员详情"
      width="600px"
    >
      <div v-if="selectedMember" class="member-detail">
        <div class="detail-header">
          <el-avatar :src="selectedMember.avatar" :size="100">{{ selectedMember.name[0] }}</el-avatar>
          <div class="detail-info">
            <h2>{{ selectedMember.name }}</h2>
            <p>{{ selectedMember.relation }} · {{ selectedMember.role }}</p>
            <p>加入时间: {{ formatDate(selectedMember.joinDate) }}</p>
          </div>
        </div>

        <el-divider />

        <div class="detail-stats">
          <h3>财务统计</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="label">本月收入</span>
              <span class="value income">¥{{ selectedMember.monthlyIncome.toLocaleString() }}</span>
            </div>
            <div class="stat-item">
              <span class="label">本月支出</span>
              <span class="value expense">¥{{ selectedMember.monthlyExpense.toLocaleString() }}</span>
            </div>
            <div class="stat-item">
              <span class="label">年度收入</span>
              <span class="value income">¥{{ selectedMember.yearlyIncome.toLocaleString() }}</span>
            </div>
            <div class="stat-item">
              <span class="label">年度支出</span>
              <span class="value expense">¥{{ selectedMember.yearlyExpense.toLocaleString() }}</span>
            </div>
          </div>
        </div>

        <el-divider />

        <div class="detail-permissions">
          <h3>权限详情</h3>
          <div class="permission-grid">
            <div
              v-for="permission in selectedMember.permissions"
              :key="permission"
              class="permission-item"
            >
              <el-icon class="permission-icon"><Check /></el-icon>
              <span>{{ getPermissionLabel(permission) }}</span>
            </div>
          </div>
        </div>

        <el-divider />

        <div class="detail-contact">
          <h3>联系信息</h3>
          <div class="contact-info">
            <p><strong>手机号:</strong> {{ selectedMember.phone || '未设置' }}</p>
            <p><strong>邮箱:</strong> {{ selectedMember.email || '未设置' }}</p>
            <p><strong>生日:</strong> {{ selectedMember.birthday || '未设置' }}</p>
            <p><strong>备注:</strong> {{ selectedMember.remark || '无' }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import dayjs from 'dayjs'

export default {
  name: 'FamilyMembers',
  setup() {
    const showAddDialog = ref(false)
    const showDetailDialog = ref(false)
    const editingMember = ref(null)
    const selectedMember = ref(null)
    const saving = ref(false)
    const memberFormRef = ref(null)

    // 成员表单
    const memberForm = reactive({
      name: '',
      relation: '',
      role: '',
      phone: '',
      email: '',
      birthday: '',
      permissions: [],
      remark: ''
    })

    // 表单验证规则
    const memberRules = {
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
      ],
      relation: [
        { required: true, message: '请选择关系', trigger: 'change' }
      ],
      role: [
        { required: true, message: '请选择角色', trigger: 'change' }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      email: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    }

    // 家庭成员数据
    const familyMembers = reactive([
      {
        id: 1,
        name: '张三',
        relation: '户主',
        role: '户主',
        avatar: '/src/assets/preview.jpg',
        phone: '***********',
        email: '<EMAIL>',
        birthday: '1985-06-15',
        joinDate: '2024-01-01',
        status: 'active',
        monthlyIncome: 12000,
        monthlyExpense: 8600,
        yearlyIncome: 144000,
        yearlyExpense: 103200,
        permissions: ['view_records', 'add_records', 'edit_records', 'delete_records', 'manage_budget', 'view_reports', 'manage_accounts'],
        remark: '家庭户主，负责整体财务管理'
      },
      {
        id: 2,
        name: '李四',
        relation: '配偶',
        role: '管理员',
        avatar: '/src/assets/preview.jpg',
        phone: '***********',
        email: '<EMAIL>',
        birthday: '1987-03-20',
        joinDate: '2024-01-01',
        status: 'active',
        monthlyIncome: 9500,
        monthlyExpense: 6200,
        yearlyIncome: 114000,
        yearlyExpense: 74400,
        permissions: ['view_records', 'add_records', 'edit_records', 'manage_budget', 'view_reports'],
        remark: '配偶，协助管理家庭财务'
      },
      {
        id: 3,
        name: '张小明',
        relation: '子女',
        role: '成员',
        avatar: '/src/assets/preview.jpg',
        phone: '***********',
        email: '<EMAIL>',
        birthday: '2005-09-10',
        joinDate: '2024-01-01',
        status: 'active',
        monthlyIncome: 800,
        monthlyExpense: 2800,
        yearlyIncome: 9600,
        yearlyExpense: 33600,
        permissions: ['view_records', 'add_records'],
        remark: '在校学生，有少量兼职收入'
      },
      {
        id: 4,
        name: '张奶奶',
        relation: '父母',
        role: '只读',
        avatar: '/src/assets/preview.jpg',
        phone: '13800138004',
        email: '',
        birthday: '1950-12-05',
        joinDate: '2024-01-01',
        status: 'active',
        monthlyIncome: 3000,
        monthlyExpense: 1000,
        yearlyIncome: 36000,
        yearlyExpense: 12000,
        permissions: ['view_records'],
        remark: '退休老人，有退休金收入'
      }
    ])

    // 计算净收入
    familyMembers.forEach(member => {
      member.netIncome = member.monthlyIncome - member.monthlyExpense
    })

    return {
      showAddDialog,
      showDetailDialog,
      editingMember,
      selectedMember,
      saving,
      memberFormRef,
      memberForm,
      memberRules,
      familyMembers
    }
  },
  methods: {
    // 获取权限类型
    getPermissionType(permission) {
      const typeMap = {
        'view_records': 'info',
        'add_records': 'success',
        'edit_records': 'warning',
        'delete_records': 'danger',
        'manage_budget': 'primary',
        'view_reports': 'info',
        'manage_accounts': 'warning'
      }
      return typeMap[permission] || 'info'
    },

    // 获取权限标签
    getPermissionLabel(permission) {
      const labelMap = {
        'view_records': '查看记录',
        'add_records': '添加记录',
        'edit_records': '编辑记录',
        'delete_records': '删除记录',
        'manage_budget': '预算管理',
        'view_reports': '查看报表',
        'manage_accounts': '账户管理'
      }
      return labelMap[permission] || permission
    },

    // 编辑成员
    editMember(member) {
      this.editingMember = member
      Object.assign(this.memberForm, {
        name: member.name,
        relation: member.relation,
        role: member.role,
        phone: member.phone,
        email: member.email,
        birthday: member.birthday,
        permissions: [...member.permissions],
        remark: member.remark
      })
      this.showAddDialog = true
    },

    // 查看成员详情
    viewMemberDetails(member) {
      this.selectedMember = member
      this.showDetailDialog = true
    },

    // 删除成员
    deleteMember(member) {
      this.$confirm(`确定要删除成员 ${member.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.familyMembers.findIndex(m => m.id === member.id)
        if (index > -1) {
          this.familyMembers.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 保存成员
    async saveMember() {
      try {
        const valid = await this.$refs.memberFormRef.validate()
        if (!valid) return

        this.saving = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (this.editingMember) {
          // 更新成员
          Object.assign(this.editingMember, this.memberForm)
          this.$message.success('更新成功')
        } else {
          // 添加新成员
          const newMember = {
            id: Date.now(),
            ...this.memberForm,
            avatar: '/src/assets/preview.jpg',
            joinDate: dayjs().format('YYYY-MM-DD'),
            status: 'active',
            monthlyIncome: 0,
            monthlyExpense: 0,
            yearlyIncome: 0,
            yearlyExpense: 0,
            netIncome: 0
          }
          this.familyMembers.push(newMember)
          this.$message.success('添加成功')
        }

        this.showAddDialog = false
        this.resetMemberForm()
      } catch (error) {
        this.$message.error('操作失败，请重试')
      } finally {
        this.saving = false
      }
    },

    // 重置成员表单
    resetMemberForm() {
      this.editingMember = null
      Object.assign(this.memberForm, {
        name: '',
        relation: '',
        role: '',
        phone: '',
        email: '',
        birthday: '',
        permissions: [],
        remark: ''
      })
      this.$refs.memberFormRef?.clearValidate()
    },

    // 导出成员数据
    exportMembers() {
      this.$message.info('导出功能开发中...')
    },

    // 格式化日期
    formatDate(dateString) {
      return dayjs(dateString).format('YYYY年MM月DD日')
    }
  }
}
</script>

<style scoped>
.family-members {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 25px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: flex-start;
  gap: 15px;
  margin-bottom: 25px;
}

/* 成员网格 */
.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

/* 成员卡片 */
.member-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.member-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 成员头部 */
.member-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.member-status {
  position: absolute;
  top: -5px;
  right: -5px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.member-status.active {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.member-status.inactive {
  background: linear-gradient(135deg, #d9d9d9, #f0f0f0);
  color: #666;
}

/* 成员信息 */
.member-info {
  text-align: center;
  margin-bottom: 20px;
}

.member-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.member-role {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}

.member-relation {
  margin: 0;
  font-size: 13px;
  color: #7f8c8d;
}

/* 成员统计 */
.member-stats {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 13px;
  color: #7f8c8d;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
}

.stat-value.income {
  color: #52c41a;
}

.stat-value.expense {
  color: #ff4d4f;
}

/* 权限设置 */
.member-permissions {
  margin-bottom: 20px;
}

.member-permissions h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* 成员操作 */
.member-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.member-actions .el-button {
  flex: 1;
  max-width: 80px;
}

/* 成员详情 */
.member-detail {
  padding: 10px 0;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.detail-info p {
  margin: 0 0 4px 0;
  color: #7f8c8d;
  font-size: 14px;
}

/* 详情统计 */
.detail-stats h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-item .label {
  font-size: 14px;
  color: #7f8c8d;
}

.stat-item .value {
  font-size: 16px;
  font-weight: 600;
}

.stat-item .value.income {
  color: #52c41a;
}

.stat-item .value.expense {
  color: #ff4d4f;
}

/* 权限详情 */
.detail-permissions h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.permission-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.permission-icon {
  color: #52c41a;
  font-size: 16px;
}

/* 联系信息 */
.detail-contact h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.contact-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #2c3e50;
  line-height: 1.6;
}

.contact-info strong {
  color: #7f8c8d;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .members-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .family-members {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .members-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .member-card {
    padding: 20px;
  }

  .action-bar {
    flex-direction: column;
    gap: 10px;
  }

  .action-bar .el-button {
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .permission-grid {
    grid-template-columns: 1fr;
  }

  .detail-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .family-members {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .member-card {
    padding: 15px;
  }

  .member-actions {
    flex-direction: column;
  }

  .member-actions .el-button {
    max-width: none;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-dialog) {
  border-radius: 16px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-tag) {
  border-radius: 6px;
  font-size: 12px;
}

:deep(.el-checkbox) {
  margin-right: 15px;
  margin-bottom: 8px;
}

:deep(.el-checkbox__label) {
  font-size: 14px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

:deep(.el-avatar) {
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-divider) {
  margin: 20px 0;
}
</style>
