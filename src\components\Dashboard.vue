<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><Odometer /></el-icon>
        家庭财务仪表盘
      </h2>
      <p class="page-subtitle">{{ currentDate }} | 欢迎回来，{{ nickname }}</p>
    </div>

    <!-- 快速统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card income">
        <div class="stat-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <h3>本月收入</h3>
          <p class="stat-value">¥{{ monthlyIncome.toLocaleString() }}</p>
          <span class="stat-change positive">+{{ incomeChange }}%</span>
        </div>
      </div>

      <div class="stat-card expense">
        <div class="stat-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <h3>本月支出</h3>
          <p class="stat-value">¥{{ monthlyExpense.toLocaleString() }}</p>
          <span class="stat-change negative">+{{ expenseChange }}%</span>
        </div>
      </div>

      <div class="stat-card balance">
        <div class="stat-icon">
          <el-icon><Wallet /></el-icon>
        </div>
        <div class="stat-content">
          <h3>账户余额</h3>
          <p class="stat-value">¥{{ totalBalance.toLocaleString() }}</p>
          <span class="stat-change">{{ balanceAccounts }}个账户</span>
        </div>
      </div>

      <div class="stat-card budget">
        <div class="stat-icon">
          <el-icon><DataAnalysis /></el-icon>
        </div>
        <div class="stat-content">
          <h3>预算执行</h3>
          <p class="stat-value">{{ budgetUsage }}%</p>
          <span class="stat-change">剩余{{ budgetRemaining }}天</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-grid">
      <!-- 左侧内容 -->
      <div class="left-content">
        <!-- 最近交易 -->
        <div class="content-card">
          <div class="card-header">
            <h3>最近交易</h3>
            <el-button type="primary" size="small" @click="toIncomeRecord">记一笔</el-button>
          </div>
          <div class="transaction-list">
            <div 
              v-for="transaction in recentTransactions" 
              :key="transaction.id"
              class="transaction-item"
            >
              <div class="transaction-icon" :class="transaction.type">
                <el-icon v-if="transaction.type === 'income'"><Plus /></el-icon>
                <el-icon v-else><Minus /></el-icon>
              </div>
              <div class="transaction-info">
                <p class="transaction-title">{{ transaction.title }}</p>
                <p class="transaction-category">{{ transaction.category }} · {{ transaction.account }}</p>
              </div>
              <div class="transaction-amount" :class="transaction.type">
                {{ transaction.type === 'income' ? '+' : '-' }}¥{{ transaction.amount.toLocaleString() }}
              </div>
            </div>
          </div>
          <div class="card-footer">
            <el-button type="text" @click="toExpenseRecord">查看全部交易</el-button>
          </div>
        </div>

        <!-- 家庭成员概览 -->
        <div class="content-card">
          <div class="card-header">
            <h3>家庭成员</h3>
            <el-button type="text" size="small" @click="toFamilyMembers">管理</el-button>
          </div>
          <div class="member-list">
            <div 
              v-for="member in familyMembers" 
              :key="member.id"
              class="member-item"
            >
              <el-avatar :src="member.avatar" :size="40">{{ member.name[0] }}</el-avatar>
              <div class="member-info">
                <p class="member-name">{{ member.name }}</p>
                <p class="member-role">{{ member.role }}</p>
              </div>
              <div class="member-stats">
                <p class="member-expense">本月支出: ¥{{ member.monthlyExpense.toLocaleString() }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <!-- 收支趋势图 -->
        <div class="content-card chart-card">
          <div class="card-header">
            <h3>收支趋势</h3>
            <el-select v-model="chartPeriod" size="small" style="width: 100px">
              <el-option label="本周" value="week" />
              <el-option label="本月" value="month" />
              <el-option label="本年" value="year" />
            </el-select>
          </div>
          <div class="chart-container">
            <canvas ref="trendChart" width="400" height="200"></canvas>
          </div>
        </div>

        <!-- 支出分类 -->
        <div class="content-card">
          <div class="card-header">
            <h3>支出分类</h3>
            <el-button type="text" size="small" @click="toCategory">管理分类</el-button>
          </div>
          <div class="category-list">
            <div 
              v-for="category in expenseCategories" 
              :key="category.name"
              class="category-item"
            >
              <div class="category-info">
                <span class="category-name">{{ category.name }}</span>
                <span class="category-amount">¥{{ category.amount.toLocaleString() }}</span>
              </div>
              <div class="category-progress">
                <el-progress 
                  :percentage="category.percentage" 
                  :color="category.color"
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="content-card">
          <div class="card-header">
            <h3>快速操作</h3>
          </div>
          <div class="quick-actions">
            <el-button type="primary" @click="toIncomeRecord" icon="Plus">记录收入</el-button>
            <el-button type="danger" @click="toIncomeRecord" icon="Minus">记录支出</el-button>
            <el-button type="info" @click="toAccounts" icon="CreditCard">查看账户</el-button>
            <el-button type="warning" @click="toBudget" icon="TrendCharts">预算管理</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import dayjs from 'dayjs'

export default {
  name: 'Dashboard',
  setup() {
    const nickname = ref(localStorage.getItem('nickname') || '用户')
    const currentDate = ref(dayjs().format('YYYY年MM月DD日'))
    const chartPeriod = ref('month')
    const trendChart = ref(null)

    // 统计数据
    const monthlyIncome = ref(25800)
    const monthlyExpense = ref(18600)
    const totalBalance = ref(156800)
    const balanceAccounts = ref(5)
    const incomeChange = ref(12.5)
    const expenseChange = ref(8.3)
    const budgetUsage = ref(68)
    const budgetRemaining = ref(15)

    // 最近交易
    const recentTransactions = reactive([
      {
        id: 1,
        title: '工资收入',
        category: '工资',
        account: '工商银行',
        amount: 8500,
        type: 'income',
        date: '2024-06-24'
      },
      {
        id: 2,
        title: '超市购物',
        category: '日用品',
        account: '支付宝',
        amount: 268,
        type: 'expense',
        date: '2024-06-23'
      },
      {
        id: 3,
        title: '房租',
        category: '住房',
        account: '建设银行',
        amount: 3500,
        type: 'expense',
        date: '2024-06-22'
      },
      {
        id: 4,
        title: '兼职收入',
        category: '兼职',
        account: '微信',
        amount: 1200,
        type: 'income',
        date: '2024-06-21'
      },
      {
        id: 5,
        title: '餐饮消费',
        category: '餐饮',
        account: '信用卡',
        amount: 156,
        type: 'expense',
        date: '2024-06-21'
      }
    ])

    // 家庭成员
    const familyMembers = reactive([
      {
        id: 1,
        name: '张三',
        role: '户主',
        avatar: '/src/assets/preview.jpg',
        monthlyExpense: 8600
      },
      {
        id: 2,
        name: '李四',
        role: '配偶',
        avatar: '/src/assets/preview.jpg',
        monthlyExpense: 6200
      },
      {
        id: 3,
        name: '张小明',
        role: '子女',
        avatar: '/src/assets/preview.jpg',
        monthlyExpense: 2800
      },
      {
        id: 4,
        name: '张奶奶',
        role: '长辈',
        avatar: '/src/assets/preview.jpg',
        monthlyExpense: 1000
      }
    ])

    // 支出分类
    const expenseCategories = reactive([
      { name: '餐饮', amount: 4200, percentage: 85, color: '#ff6b6b' },
      { name: '交通', amount: 1800, percentage: 60, color: '#4ecdc4' },
      { name: '购物', amount: 3200, percentage: 75, color: '#45b7d1' },
      { name: '娱乐', amount: 1200, percentage: 40, color: '#96ceb4' },
      { name: '医疗', amount: 800, percentage: 25, color: '#feca57' }
    ])

    return {
      nickname,
      currentDate,
      chartPeriod,
      trendChart,
      monthlyIncome,
      monthlyExpense,
      totalBalance,
      balanceAccounts,
      incomeChange,
      expenseChange,
      budgetUsage,
      budgetRemaining,
      recentTransactions,
      familyMembers,
      expenseCategories
    }
  },
  methods: {
    // 导航方法
    toIncomeRecord() {
      this.$router.push('/admin/IncomeRecord')
    },
    toExpenseRecord() {
      this.$router.push('/admin/ExpenseRecord')
    },
    toFamilyMembers() {
      this.$router.push('/admin/FamilyMembers')
    },
    toCategory() {
      this.$router.push('/admin/Category')
    },
    toAccounts() {
      this.$router.push('/admin/Accounts')
    },
    toBudget() {
      this.$router.push('/admin/Budget')
    }
  },
  mounted() {
    // 初始化图表
    this.initChart()
  },
  methods: {
    initChart() {
      // 这里可以使用Chart.js或ECharts来绘制图表
      // 暂时留空，后续可以添加具体的图表实现
    }
  }
}
</script>

<style scoped>
.dashboard {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 25px;
  text-align: center;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-card.income .stat-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-card.expense .stat-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.stat-card.balance .stat-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.stat-card.budget .stat-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.stat-content h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

.stat-value {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #52c41a;
}

.stat-change.negative {
  color: #ff4d4f;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  height: calc(100vh - 300px);
  min-height: 500px;
}

.left-content,
.right-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

/* 内容卡片 */
.content-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.card-footer {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
}

/* 交易列表 */
.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.transaction-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.transaction-icon.income {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.transaction-icon.expense {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.transaction-category {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
}

.transaction-amount {
  font-size: 16px;
  font-weight: 600;
}

.transaction-amount.income {
  color: #52c41a;
}

.transaction-amount.expense {
  color: #ff4d4f;
}

/* 成员列表 */
.member-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.member-info {
  flex: 1;
}

.member-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.member-role {
  margin: 0;
  font-size: 12px;
  color: #7f8c8d;
}

.member-stats {
  text-align: right;
}

.member-expense {
  margin: 0;
  font-size: 12px;
  color: #ff4d4f;
}

/* 图表卡片 */
.chart-card {
  min-height: 280px;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 分类列表 */
.category-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.category-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.category-amount {
  font-size: 14px;
  font-weight: 600;
  color: #ff4d4f;
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.quick-actions .el-button {
  height: 45px;
  border-radius: 12px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .content-grid {
    gap: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .stat-card {
    padding: 15px;
  }

  .content-card {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .dashboard {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .stat-card {
    padding: 12px;
    gap: 10px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 20px;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-progress-bar__outer) {
  border-radius: 3px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 3px;
}

:deep(.el-avatar) {
  border: 2px solid rgba(255, 255, 255, 0.8);
}
</style>
