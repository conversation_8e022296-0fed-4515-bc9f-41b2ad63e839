<template>
  <div class="accounts">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><CreditCard /></el-icon>
        资金账户管理
      </h2>
      <p class="page-subtitle">管理您的银行卡、电子钱包等资金存储位置</p>
    </div>

    <!-- 总览统计 -->
    <div class="overview-section">
      <div class="overview-grid">
        <div class="overview-card total">
          <div class="card-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="card-content">
            <h3>总资产</h3>
            <p class="amount">¥{{ totalAssets.toLocaleString() }}</p>
            <span class="change positive">+{{ assetChange }}%</span>
          </div>
        </div>

        <div class="overview-card accounts">
          <div class="card-icon">
            <el-icon><CreditCard /></el-icon>
          </div>
          <div class="card-content">
            <h3>账户数量</h3>
            <p class="amount">{{ accountList.length }}个</p>
            <span class="change">{{ activeAccounts }}个活跃</span>
          </div>
        </div>

        <div class="overview-card income">
          <div class="card-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="card-content">
            <h3>本月收入</h3>
            <p class="amount">¥{{ monthlyIncome.toLocaleString() }}</p>
            <span class="change positive">+{{ incomeChange }}%</span>
          </div>
        </div>

        <div class="overview-card expense">
          <div class="card-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="card-content">
            <h3>本月支出</h3>
            <p class="amount">¥{{ monthlyExpense.toLocaleString() }}</p>
            <span class="change negative">+{{ expenseChange }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showAddDialog = true" icon="Plus">添加账户</el-button>
      <el-button type="info" @click="refreshAccounts" icon="Refresh">刷新余额</el-button>
      <el-button type="warning" @click="exportAccounts" icon="Download">导出数据</el-button>
    </div>

    <!-- 账户列表 -->
    <div class="accounts-grid">
      <div
        v-for="account in accountList"
        :key="account.id"
        class="account-card"
        :class="account.type"
      >
        <div class="account-header">
          <div class="account-icon" :class="account.type">
            <el-icon v-if="account.type === 'bank'"><CreditCard /></el-icon>
            <el-icon v-else-if="account.type === 'alipay'"><Money /></el-icon>
            <el-icon v-else-if="account.type === 'wechat'"><ChatDotRound /></el-icon>
            <el-icon v-else><Wallet /></el-icon>
          </div>
          <div class="account-status" :class="account.status">
            {{ account.status === 'active' ? '正常' : '停用' }}
          </div>
        </div>

        <div class="account-info">
          <h3 class="account-name">{{ account.name }}</h3>
          <p class="account-number">{{ account.number }}</p>
          <p class="account-type">{{ getAccountTypeLabel(account.type) }}</p>
        </div>

        <div class="account-balance">
          <p class="balance-label">账户余额</p>
          <p class="balance-amount">¥{{ account.balance.toLocaleString() }}</p>
          <p class="balance-update">更新于 {{ formatDate(account.lastUpdate) }}</p>
        </div>

        <div class="account-stats">
          <div class="stat-item">
            <span class="stat-label">本月收入</span>
            <span class="stat-value income">+¥{{ account.monthlyIncome.toLocaleString() }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">本月支出</span>
            <span class="stat-value expense">-¥{{ account.monthlyExpense.toLocaleString() }}</span>
          </div>
        </div>

        <div class="account-actions">
          <el-button type="primary" size="small" @click="editAccount(account)" icon="Edit">编辑</el-button>
          <el-button type="info" size="small" @click="viewTransactions(account)" icon="View">交易</el-button>
          <el-button
            type="danger"
            size="small"
            @click="deleteAccount(account)"
            icon="Delete"
            :disabled="account.balance > 0"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加/编辑账户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingAccount ? '编辑账户' : '添加账户'"
      width="500px"
      @close="resetAccountForm"
    >
      <el-form
        :model="accountForm"
        :rules="accountRules"
        ref="accountFormRef"
        label-width="100px"
      >
        <el-form-item label="账户名称" prop="name">
          <el-input v-model="accountForm.name" placeholder="请输入账户名称" />
        </el-form-item>

        <el-form-item label="账户类型" prop="type">
          <el-select v-model="accountForm.type" placeholder="请选择账户类型" style="width: 100%">
            <el-option label="银行卡" value="bank" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信" value="wechat" />
            <el-option label="现金" value="cash" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="账户号码" prop="number">
          <el-input v-model="accountForm.number" placeholder="请输入账户号码或卡号" />
        </el-form-item>

        <el-form-item label="初始余额" prop="balance">
          <el-input
            v-model="accountForm.balance"
            placeholder="请输入初始余额"
            type="number"
            step="0.01"
            min="0"
          >
            <template #prefix>¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="开户银行" prop="bank" v-if="accountForm.type === 'bank'">
          <el-select v-model="accountForm.bank" placeholder="请选择开户银行" style="width: 100%">
            <el-option label="中国工商银行" value="工商银行" />
            <el-option label="中国建设银行" value="建设银行" />
            <el-option label="中国农业银行" value="农业银行" />
            <el-option label="中国银行" value="中国银行" />
            <el-option label="招商银行" value="招商银行" />
            <el-option label="交通银行" value="交通银行" />
            <el-option label="其他银行" value="其他银行" />
          </el-select>
        </el-form-item>

        <el-form-item label="账户状态" prop="status">
          <el-radio-group v-model="accountForm.status">
            <el-radio value="active">正常</el-radio>
            <el-radio value="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="accountForm.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAccount" :loading="saving">
          {{ editingAccount ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 交易记录对话框 -->
    <el-dialog
      v-model="showTransactionDialog"
      title="账户交易记录"
      width="800px"
    >
      <div v-if="selectedAccount" class="transaction-content">
        <div class="transaction-header">
          <h3>{{ selectedAccount.name }} 交易记录</h3>
          <p>账户余额: ¥{{ selectedAccount.balance.toLocaleString() }}</p>
        </div>

        <el-table :data="accountTransactions" style="width: 100%">
          <el-table-column prop="date" label="日期" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.date) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="type" label="类型" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.type === 'income' ? 'success' : 'danger'" size="small">
                {{ scope.row.type === 'income' ? '收入' : '支出' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="category" label="分类" width="100" />
          
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="scope">
              <span :class="scope.row.type === 'income' ? 'amount-income' : 'amount-expense'">
                {{ scope.row.type === 'income' ? '+' : '-' }}¥{{ scope.row.amount.toLocaleString() }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="remark" label="备注" min-width="150" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import dayjs from 'dayjs'

export default {
  name: 'Accounts',
  setup() {
    const showAddDialog = ref(false)
    const showTransactionDialog = ref(false)
    const editingAccount = ref(null)
    const selectedAccount = ref(null)
    const saving = ref(false)
    const accountFormRef = ref(null)

    // 账户表单
    const accountForm = reactive({
      name: '',
      type: '',
      number: '',
      balance: '',
      bank: '',
      status: 'active',
      remark: ''
    })

    // 表单验证规则
    const accountRules = {
      name: [
        { required: true, message: '请输入账户名称', trigger: 'blur' }
      ],
      type: [
        { required: true, message: '请选择账户类型', trigger: 'change' }
      ],
      number: [
        { required: true, message: '请输入账户号码', trigger: 'blur' }
      ],
      balance: [
        { required: true, message: '请输入初始余额', trigger: 'blur' },
        { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
      ]
    }

    // 账户列表
    const accountList = reactive([
      {
        id: 1,
        name: '工商银行储蓄卡',
        type: 'bank',
        number: '**** **** **** 1234',
        bank: '工商银行',
        balance: 25600,
        status: 'active',
        monthlyIncome: 12000,
        monthlyExpense: 8600,
        lastUpdate: '2024-06-24 10:30:00',
        remark: '主要工资卡'
      },
      {
        id: 2,
        name: '建设银行信用卡',
        type: 'bank',
        number: '**** **** **** 5678',
        bank: '建设银行',
        balance: 18900,
        status: 'active',
        monthlyIncome: 0,
        monthlyExpense: 3200,
        lastUpdate: '2024-06-24 09:15:00',
        remark: '日常消费信用卡'
      },
      {
        id: 3,
        name: '支付宝',
        type: 'alipay',
        number: '138****8001',
        bank: '',
        balance: 3200,
        status: 'active',
        monthlyIncome: 1500,
        monthlyExpense: 2800,
        lastUpdate: '2024-06-24 11:45:00',
        remark: '日常小额支付'
      },
      {
        id: 4,
        name: '微信钱包',
        type: 'wechat',
        number: '138****8001',
        bank: '',
        balance: 1800,
        status: 'active',
        monthlyIncome: 800,
        monthlyExpense: 1200,
        lastUpdate: '2024-06-24 08:20:00',
        remark: '微信支付'
      },
      {
        id: 5,
        name: '现金',
        type: 'cash',
        number: '现金',
        bank: '',
        balance: 500,
        status: 'active',
        monthlyIncome: 200,
        monthlyExpense: 300,
        lastUpdate: '2024-06-23 20:00:00',
        remark: '日常现金'
      }
    ])

    // 模拟交易记录
    const accountTransactions = reactive([
      {
        id: 1,
        date: '2024-06-24 09:00:00',
        type: 'income',
        category: '工资',
        amount: 8500,
        remark: '6月工资'
      },
      {
        id: 2,
        date: '2024-06-23 12:30:00',
        type: 'expense',
        category: '餐饮',
        amount: 268,
        remark: '午餐'
      },
      {
        id: 3,
        date: '2024-06-22 18:45:00',
        type: 'expense',
        category: '交通',
        amount: 156,
        remark: '打车费'
      }
    ])

    // 计算总资产
    const totalAssets = computed(() => {
      return accountList.reduce((sum, account) => sum + account.balance, 0)
    })

    // 计算活跃账户数
    const activeAccounts = computed(() => {
      return accountList.filter(account => account.status === 'active').length
    })

    // 计算本月收入
    const monthlyIncome = computed(() => {
      return accountList.reduce((sum, account) => sum + account.monthlyIncome, 0)
    })

    // 计算本月支出
    const monthlyExpense = computed(() => {
      return accountList.reduce((sum, account) => sum + account.monthlyExpense, 0)
    })

    // 模拟变化百分比
    const assetChange = ref(5.2)
    const incomeChange = ref(12.5)
    const expenseChange = ref(8.3)

    return {
      showAddDialog,
      showTransactionDialog,
      editingAccount,
      selectedAccount,
      saving,
      accountFormRef,
      accountForm,
      accountRules,
      accountList,
      accountTransactions,
      totalAssets,
      activeAccounts,
      monthlyIncome,
      monthlyExpense,
      assetChange,
      incomeChange,
      expenseChange
    }
  },
  methods: {
    // 获取账户类型标签
    getAccountTypeLabel(type) {
      const typeMap = {
        'bank': '银行卡',
        'alipay': '支付宝',
        'wechat': '微信',
        'cash': '现金',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    // 编辑账户
    editAccount(account) {
      this.editingAccount = account
      Object.assign(this.accountForm, {
        name: account.name,
        type: account.type,
        number: account.number,
        balance: account.balance.toString(),
        bank: account.bank,
        status: account.status,
        remark: account.remark
      })
      this.showAddDialog = true
    },

    // 查看交易记录
    viewTransactions(account) {
      this.selectedAccount = account
      this.showTransactionDialog = true
    },

    // 删除账户
    deleteAccount(account) {
      if (account.balance > 0) {
        this.$message.warning('账户余额不为零，无法删除')
        return
      }

      this.$confirm(`确定要删除账户 ${account.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.accountList.findIndex(a => a.id === account.id)
        if (index > -1) {
          this.accountList.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 保存账户
    async saveAccount() {
      try {
        const valid = await this.$refs.accountFormRef.validate()
        if (!valid) return

        this.saving = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (this.editingAccount) {
          // 更新账户
          Object.assign(this.editingAccount, {
            ...this.accountForm,
            balance: parseFloat(this.accountForm.balance)
          })
          this.$message.success('更新成功')
        } else {
          // 添加新账户
          const newAccount = {
            id: Date.now(),
            ...this.accountForm,
            balance: parseFloat(this.accountForm.balance),
            monthlyIncome: 0,
            monthlyExpense: 0,
            lastUpdate: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
          this.accountList.push(newAccount)
          this.$message.success('添加成功')
        }

        this.showAddDialog = false
        this.resetAccountForm()
      } catch (error) {
        this.$message.error('操作失败，请重试')
      } finally {
        this.saving = false
      }
    },

    // 重置账户表单
    resetAccountForm() {
      this.editingAccount = null
      Object.assign(this.accountForm, {
        name: '',
        type: '',
        number: '',
        balance: '',
        bank: '',
        status: 'active',
        remark: ''
      })
      this.$refs.accountFormRef?.clearValidate()
    },

    // 刷新账户余额
    refreshAccounts() {
      this.$message.info('刷新功能开发中...')
    },

    // 导出账户数据
    exportAccounts() {
      this.$message.info('导出功能开发中...')
    },

    // 格式化日期
    formatDate(dateString) {
      return dayjs(dateString).format('MM-DD HH:mm')
    }
  }
}
</script>

<style scoped>
.accounts {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 25px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 总览区域 */
.overview-section {
  margin-bottom: 25px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.overview-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.overview-card.total .card-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.overview-card.accounts .card-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.overview-card.income .card-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.overview-card.expense .card-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.card-content h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

.card-content .amount {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.card-content .change {
  font-size: 12px;
  font-weight: 500;
}

.change.positive {
  color: #52c41a;
}

.change.negative {
  color: #ff4d4f;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: flex-start;
  gap: 15px;
  margin-bottom: 25px;
}

/* 账户网格 */
.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

/* 账户卡片 */
.account-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.account-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

/* 账户头部 */
.account-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.account-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.account-icon.bank {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.account-icon.alipay {
  background: linear-gradient(135deg, #1677ff, #69b1ff);
}

.account-icon.wechat {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.account-icon.cash {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.account-icon.other {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.account-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.account-status.active {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.account-status.inactive {
  background: linear-gradient(135deg, #d9d9d9, #f0f0f0);
  color: #666;
}

/* 账户信息 */
.account-info {
  margin-bottom: 20px;
}

.account-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.account-number {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #7f8c8d;
  font-family: monospace;
}

.account-type {
  margin: 0;
  font-size: 13px;
  color: #409eff;
  font-weight: 500;
}

/* 账户余额 */
.account-balance {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  text-align: center;
}

.balance-label {
  margin: 0 0 5px 0;
  font-size: 13px;
  color: #7f8c8d;
}

.balance-amount {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.balance-update {
  margin: 0;
  font-size: 12px;
  color: #bdc3c7;
}

/* 账户统计 */
.account-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  font-size: 13px;
  color: #7f8c8d;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
}

.stat-value.income {
  color: #52c41a;
}

.stat-value.expense {
  color: #ff4d4f;
}

/* 账户操作 */
.account-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.account-actions .el-button {
  flex: 1;
  max-width: 80px;
}

/* 交易记录 */
.transaction-content {
  padding: 10px 0;
}

.transaction-header {
  margin-bottom: 20px;
  text-align: center;
}

.transaction-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.transaction-header p {
  margin: 0;
  font-size: 16px;
  color: #409eff;
  font-weight: 500;
}

/* 表格样式 */
.amount-income {
  color: #52c41a;
  font-weight: 600;
}

.amount-expense {
  color: #ff4d4f;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .accounts-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .accounts {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .accounts-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .account-card {
    padding: 20px;
  }

  .action-bar {
    flex-direction: column;
    gap: 10px;
  }

  .action-bar .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .accounts {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .overview-card {
    padding: 15px;
    gap: 10px;
  }

  .card-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .card-content .amount {
    font-size: 20px;
  }

  .account-card {
    padding: 15px;
  }

  .account-actions {
    flex-direction: column;
  }

  .account-actions .el-button {
    max-width: none;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-dialog) {
  border-radius: 16px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-radio-button__inner) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table th) {
  background: rgba(255, 255, 255, 0.6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-table tr:hover > td) {
  background: rgba(255, 255, 255, 0.8);
}

:deep(.el-tag) {
  border-radius: 6px;
}
</style>
